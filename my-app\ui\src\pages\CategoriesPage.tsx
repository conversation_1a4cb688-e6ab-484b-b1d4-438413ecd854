import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { api } from '@/lib/serverComm';
import { Search, Store, Utensils, Car, Heart, Briefcase, Home, Scissors, Dumbbell, GraduationCap, Wrench } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
  business_count: number;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  store: Store,
  utensils: Utensils,
  car: Car,
  heart: Heart,
  briefcase: Briefcase,
  home: Home,
  scissors: Scissors,
  dumbbell: Dumbbell,
  'graduation-cap': GraduationCap,
  wrench: Wrench,
};

export function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategories() {
      try {
        const response = await api.getCategories();
        const sortedCategories = response.categories.sort((a: Category, b: Category) => 
          b.business_count - a.business_count
        );
        setCategories(sortedCategories);
        setFilteredCategories(sortedCategories);
      } catch (err) {
        setError('Failed to load categories');
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchCategories();
  }, []);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCategories(categories);
    } else {
      const filtered = categories.filter(category =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        category.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCategories(filtered);
    }
  }, [searchQuery, categories]);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">Business Categories</h1>
            <p className="text-muted-foreground">Loading categories...</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 12 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4"></div>
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded w-20 mx-auto"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Business Categories</h1>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">Business Categories</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Explore our comprehensive directory of local businesses organized by category. 
            Find exactly what you're looking for in your area.
          </p>
        </div>

        {/* Search */}
        <div className="max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search categories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredCategories.map((category) => {
            const IconComponent = iconMap[category.icon] || Store;
            
            return (
              <Link 
                key={category.id} 
                to={`/categories/${category.slug}`}
                className="group"
              >
                <Card className="h-full transition-all duration-200 hover:shadow-lg hover:-translate-y-1 group-hover:border-primary/50">
                  <CardContent className="p-6 text-center space-y-4">
                    <div className="w-16 h-16 mx-auto flex items-center justify-center rounded-full bg-primary/10 text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      <IconComponent className="w-8 h-8" />
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
                        {category.name}
                      </h3>
                      
                      {category.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {category.description}
                        </p>
                      )}
                      
                      <Badge variant="secondary" className="text-xs">
                        {category.business_count} {category.business_count === 1 ? 'business' : 'businesses'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* No Results */}
        {filteredCategories.length === 0 && searchQuery && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">
              No categories found matching "{searchQuery}". Try a different search term.
            </p>
          </div>
        )}

        {/* Stats */}
        <div className="text-center pt-8 border-t">
          <p className="text-muted-foreground">
            Showing {filteredCategories.length} of {categories.length} categories
          </p>
        </div>
      </div>
    </div>
  );
}
