import { HeroSection } from '@/components/homepage/HeroSection';
import { CategoryHighlights } from '@/components/homepage/CategoryHighlights';
import { FeaturedBusinesses } from '@/components/homepage/FeaturedBusinesses';
import { NewBusinesses } from '@/components/homepage/NewBusinesses';
import { MarqueeBanner } from '@/components/homepage/MarqueeBanner';
import { ErrorBoundary } from '@/components/ui/error-boundary';
import { useNavigate } from 'react-router-dom';

export function HomePage() {
  const navigate = useNavigate();

  const handleSearch = (query: string, location?: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    if (location) params.set('location', location);
    navigate(`/search?${params.toString()}`);
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <ErrorBoundary>
        <HeroSection onSearch={handleSearch} />
      </ErrorBoundary>
      
      {/* Marquee Banner */}
      <ErrorBoundary>
        <MarqueeBanner />
      </ErrorBoundary>
      
      {/* Category Highlights */}
      <ErrorBoundary>
        <CategoryHighlights />
      </ErrorBoundary>
      
      {/* Featured Businesses */}
      <ErrorBoundary>
        <FeaturedBusinesses />
      </ErrorBoundary>
      
      {/* New Businesses */}
      <ErrorBoundary>
        <NewBusinesses />
      </ErrorBoundary>
      
      {/* Call to Action Section */}
      <section className="py-16 px-4 bg-primary text-primary-foreground">
        <div className="container mx-auto text-center">
          <div className="max-w-2xl mx-auto space-y-6">
            <h2 className="text-3xl font-bold">
              Ready to Grow Your Business?
            </h2>
            <p className="text-lg opacity-90">
              Join hundreds of local businesses already thriving in our directory. 
              Get discovered by customers in your area today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                onClick={() => navigate('/apply')}
                className="bg-background text-foreground px-6 py-3 rounded-lg font-semibold hover:bg-background/90 transition-colors"
              >
                List Your Business
              </button>
              <button 
                onClick={() => navigate('/businesses')}
                className="border border-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary-foreground/10 transition-colors"
              >
                Browse Directory
              </button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
