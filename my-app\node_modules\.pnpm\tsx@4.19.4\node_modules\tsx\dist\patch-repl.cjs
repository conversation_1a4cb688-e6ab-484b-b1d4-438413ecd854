"use strict";var i=Object.defineProperty;var a=(r,t)=>i(r,"name",{value:t,configurable:!0});var n=require("node:repl"),u=require("esbuild");const f=a(r=>{const{eval:t}=r,c=a(async function(e,l,s,o){try{e=(await u.transform(e,{sourcefile:s,loader:"ts",tsconfigRaw:{compilerOptions:{preserveValueImports:!0}},define:{require:"global.require"}})).code}catch{}return t.call(this,e,l,s,o)},"preEval");r.eval=c},"patchEval"),{start:p}=n;n.start=function(){const r=Reflect.apply(p,this,arguments);return f(r),r};
