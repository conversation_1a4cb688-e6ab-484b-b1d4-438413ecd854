[debug] [2025-08-02T18:53:04.264Z] ----------------------------------------------------------------------
[debug] [2025-08-02T18:53:04.278Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\shop-bze\my-app\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only auth --project demo-project --export-on-exit=./data/firebase-emulator --import=./data/firebase-emulator
[debug] [2025-08-02T18:53:04.279Z] CLI Version:   13.35.1
[debug] [2025-08-02T18:53:04.280Z] Platform:      win32
[debug] [2025-08-02T18:53:04.280Z] Node Version:  v24.4.1
[debug] [2025-08-02T18:53:04.281Z] Time:          Sat Aug 02 2025 14:53:04 GMT-0400 (Eastern Daylight Time)
[debug] [2025-08-02T18:53:04.281Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-02T18:53:05.099Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] Failed to authenticate, have you run firebase login?
[warn] !  emulators: You are not currently authenticated so some features may not work correctly. Please run firebase login to authenticate the CLI. 
[info] i  emulators: Starting emulators: auth {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth"}}
[info] i  emulators: Detected demo project ID "demo-project", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail. {"metadata":{"emulator":{"name":"hub"},"message":"Detected demo project ID \"demo-project\", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail."}}
[debug] [2025-08-02T18:53:05.305Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[warn] !  hub: emulator hub unable to start on port 4400, starting on 4401 instead. {"metadata":{"emulator":{"name":"hub"},"message":"emulator hub unable to start on port 4400, starting on 4401 instead."}}
[warn] !  logging: Logging Emulator unable to start on port 4500, starting on 4501 instead. {"metadata":{"emulator":{"name":"logging"},"message":"Logging Emulator unable to start on port 4500, starting on 4501 instead."}}
[debug] [2025-08-02T18:53:05.314Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-02T18:53:05.315Z] assigned listening specs for emulators {"user":{"ui":[{"address":"127.0.0.1","family":"IPv4","port":5604},{"address":"::1","family":"IPv6","port":5604}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":5603}],"hub":[{"address":"127.0.0.1","family":"IPv4","port":4401},{"address":"::1","family":"IPv6","port":4401}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4501}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project demo-project. This may result in unexpected behavior. 
[debug] [2025-08-02T18:53:05.327Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-demo-project.json
[info] i  auth: Importing config from C:\Users\<USER>\shop-bze\my-app\data\firebase-emulator\auth_export\config.json {"metadata":{"emulator":{"name":"auth"},"message":"Importing config from C:\\Users\\<USER>\\shop-bze\\my-app\\data\\firebase-emulator\\auth_export\\config.json"}}
[info] i  auth: Importing accounts from C:\Users\<USER>\shop-bze\my-app\data\firebase-emulator\auth_export\accounts.json {"metadata":{"emulator":{"name":"auth"},"message":"Importing accounts from C:\\Users\\<USER>\\shop-bze\\my-app\\data\\firebase-emulator\\auth_export\\accounts.json"}}
[debug] [2025-08-02T18:53:32.388Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:5604/               │
└─────────────────────────────────────────────────────────────┘

┌────────────────┬────────────────┬────────────────────────────┐
│ Emulator       │ Host:Port      │ View in Emulator UI        │
├────────────────┼────────────────┼────────────────────────────┤
│ Authentication │ 127.0.0.1:5603 │ http://127.0.0.1:5604/auth │
└────────────────┴────────────────┴────────────────────────────┘
  Emulator Hub host: 127.0.0.1 port: 4401
  Other reserved ports: 4501

Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
