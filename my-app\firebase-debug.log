[debug] [2025-08-02T19:19:51.971Z] ----------------------------------------------------------------------
[debug] [2025-08-02T19:19:51.978Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\shop-bze\my-app\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only auth --project demo-project --export-on-exit=./data/firebase-emulator --import=./data/firebase-emulator
[debug] [2025-08-02T19:19:51.979Z] CLI Version:   13.35.1
[debug] [2025-08-02T19:19:51.980Z] Platform:      win32
[debug] [2025-08-02T19:19:51.980Z] Node Version:  v24.4.1
[debug] [2025-08-02T19:19:51.980Z] Time:          Sat Aug 02 2025 15:19:51 GMT-0400 (Eastern Daylight Time)
[debug] [2025-08-02T19:19:51.981Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-02T19:19:52.669Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] Failed to authenticate, have you run firebase login?
[warn] !  emulators: You are not currently authenticated so some features may not work correctly. Please run firebase login to authenticate the CLI. 
[info] i  emulators: Starting emulators: auth {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth"}}
[info] i  emulators: Detected demo project ID "demo-project", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail. {"metadata":{"emulator":{"name":"hub"},"message":"Detected demo project ID \"demo-project\", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail."}}
[debug] [2025-08-02T19:19:52.880Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[warn] !  hub: emulator hub unable to start on port 4400, starting on 4403 instead. {"metadata":{"emulator":{"name":"hub"},"message":"emulator hub unable to start on port 4400, starting on 4403 instead."}}
[warn] !  logging: Logging Emulator unable to start on port 4500, starting on 4503 instead. {"metadata":{"emulator":{"name":"logging"},"message":"Logging Emulator unable to start on port 4500, starting on 4503 instead."}}
[debug] [2025-08-02T19:19:52.896Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-02T19:19:52.897Z] assigned listening specs for emulators {"user":{"ui":[{"address":"127.0.0.1","family":"IPv4","port":5804},{"address":"::1","family":"IPv6","port":5804}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":5803}],"hub":[{"address":"127.0.0.1","family":"IPv4","port":4403},{"address":"::1","family":"IPv6","port":4403}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4503}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-08-02T19:19:52.907Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-demo-project.json
