import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { MobileNav } from './MobileNav';
import { ModeToggle } from '@/components/mode-toggle';
import { Building, Search, Grid3X3 } from 'lucide-react';

export function PublicHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-6">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-2 font-bold text-xl">
            <Building className="h-6 w-6 text-primary" />
            <span className="hidden sm:inline">Business Directory</span>
            <span className="sm:hidden">BizDir</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <Link 
              to="/" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Home
            </Link>
            <Link 
              to="/businesses" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Businesses
            </Link>
            <Link 
              to="/categories" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Categories
            </Link>
          </nav>

          {/* Actions */}
          <div className="flex items-center gap-3">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center gap-3">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/search">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link to="/apply">List Business</Link>
              </Button>
              <ModeToggle />
            </div>

            {/* Mobile Actions */}
            <div className="md:hidden flex items-center gap-2">
              <Button variant="ghost" size="icon" asChild>
                <Link to="/search">
                  <Search className="h-5 w-5" />
                  <span className="sr-only">Search</span>
                </Link>
              </Button>
              <Button variant="ghost" size="icon" asChild>
                <Link to="/categories">
                  <Grid3X3 className="h-5 w-5" />
                  <span className="sr-only">Categories</span>
                </Link>
              </Button>
              <ModeToggle />
            </div>

            {/* Mobile Menu */}
            <MobileNav />
          </div>
        </div>
      </div>
    </header>
  );
}
