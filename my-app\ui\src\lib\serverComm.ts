import { getAuth } from 'firebase/auth';
import { app } from './firebase';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8787';

class APIError extends Error {
  constructor(public status: number, message: string) {
    super(message);
    this.name = 'APIError';
  }
}

async function getAuthToken(): Promise<string | null> {
  const auth = getAuth(app);
  const user = auth.currentUser;
  if (!user) {
    return null;
  }
  return user.getIdToken();
}

async function fetchWithAuth(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const token = await getAuthToken();
  const headers = new Headers(options.headers);
  
  if (token) {
    headers.set('Authorization', `Bearer ${token}`);
  }

  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    headers,
  });

  if (!response.ok) {
    throw new APIError(
      response.status,
      `API request failed: ${response.statusText}`
    );
  }

  return response;
}

// API endpoints
export async function getCurrentUser() {
  const response = await fetchWithAuth('/api/v1/protected/me');
  return response.json();
}

// Business Directory API endpoints
export async function getBusinesses(params?: {
  page?: number;
  limit?: number;
  category?: string;
  featured?: boolean;
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.category) searchParams.set('category', params.category);
  if (params?.featured) searchParams.set('featured', 'true');

  const response = await fetch(`${API_BASE_URL}/api/v1/businesses?${searchParams}`);
  if (!response.ok) {
    throw new APIError(response.status, `Failed to fetch businesses: ${response.statusText}`);
  }
  return response.json();
}

export async function getBusiness(slug: string) {
  const response = await fetch(`${API_BASE_URL}/api/v1/businesses/${slug}`);
  if (!response.ok) {
    throw new APIError(response.status, `Failed to fetch business: ${response.statusText}`);
  }
  return response.json();
}

export async function getCategories() {
  const response = await fetch(`${API_BASE_URL}/api/v1/categories`);
  if (!response.ok) {
    throw new APIError(response.status, `Failed to fetch categories: ${response.statusText}`);
  }
  return response.json();
}

export async function getCategoryBusinesses(slug: string, params?: {
  page?: number;
  limit?: number;
}) {
  const searchParams = new URLSearchParams();
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());

  const response = await fetch(`${API_BASE_URL}/api/v1/categories/${slug}?${searchParams}`);
  if (!response.ok) {
    throw new APIError(response.status, `Failed to fetch category businesses: ${response.statusText}`);
  }
  return response.json();
}

export async function searchBusinesses(query: string, params?: {
  page?: number;
  limit?: number;
  category?: string;
}) {
  const searchParams = new URLSearchParams();
  searchParams.set('q', query);
  if (params?.page) searchParams.set('page', params.page.toString());
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.category) searchParams.set('category', params.category);

  const response = await fetch(`${API_BASE_URL}/api/v1/search?${searchParams}`);
  if (!response.ok) {
    throw new APIError(response.status, `Failed to search businesses: ${response.statusText}`);
  }
  return response.json();
}

export async function submitBusinessApplication(data: {
  business_name: string;
  category_id: number;
  description: string;
  address: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  email: string;
  website?: string;
  contact_person: string;
}) {
  const response = await fetch(`${API_BASE_URL}/api/v1/applications`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new APIError(response.status, `Failed to submit application: ${response.statusText}`);
  }
  return response.json();
}

export async function submitReview(data: {
  business_id: number;
  rating: number;
  comment?: string;
  author_name: string;
  author_email?: string;
}) {
  const response = await fetch(`${API_BASE_URL}/api/v1/reviews`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new APIError(response.status, `Failed to submit review: ${response.statusText}`);
  }
  return response.json();
}

export const api = {
  getCurrentUser,
  getBusinesses,
  getBusiness,
  getCategories,
  getCategoryBusinesses,
  searchBusinesses,
  submitBusinessApplication,
  submitReview,
};