{"kind": "identitytoolkit#DownloadAccountResponse", "users": [{"localId": "adminTestUser123", "lastLoginAt": "**********000", "emailVerified": true, "email": "<EMAIL>", "salt": "fakeSalt", "passwordHash": "fakeHash:salt=fakeSalt:password=admin123", "passwordUpdatedAt": **********000, "validSince": "**********", "createdAt": "**********000", "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>"}], "lastRefreshAt": "2025-08-02T17:45:17.972Z", "customClaims": {"admin": true, "roles": ["admin"], "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]}}, {"localId": "kUpCde9F1EC48ygdPct0YJktFQdC", "lastLoginAt": "**********000", "emailVerified": true, "email": "<EMAIL>", "salt": "fakeSaltOld", "passwordHash": "fakeHash:salt=fakeSaltOld:password=password", "passwordUpdatedAt": **********000, "validSince": "**********", "createdAt": "**********000", "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>"}], "lastRefreshAt": "2025-08-02T17:45:17.972Z", "customClaims": {"admin": true, "roles": ["admin"], "permissions": ["admin.access", "admin.businesses", "admin.applications", "admin.reviews"]}}]}