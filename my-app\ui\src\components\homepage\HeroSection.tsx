import { useState } from 'react';
import { Search, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNavigate } from 'react-router-dom';
import { useStats } from '@/hooks/useStats';

interface HeroSectionProps {
  onSearch?: (query: string, location?: string) => void;
}

export function HeroSection({ onSearch }: HeroSectionProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [location, setLocation] = useState('');
  const navigate = useNavigate();
  const { stats, loading: statsLoading } = useStats();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      if (onSearch) {
        onSearch(searchQuery, location);
      } else {
        // Navigate to search page with query parameters
        const params = new URLSearchParams();
        params.set('q', searchQuery);
        if (location) params.set('location', location);
        navigate(`/search?${params.toString()}`);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <section className="relative bg-gradient-to-br from-primary/10 via-background to-secondary/10 py-20 px-4">
      <div className="container mx-auto max-w-4xl text-center">
        <div className="space-y-8">
          {/* Hero Title */}
          <div className="space-y-4">
            <h1 className="h1 md:text-4xl font-bold tracking-tight">
              Discover Local
              <span className="text-primary block">Businesses</span>
            </h1>
            <p className="text-md text-muted-foreground max-w-2xl mx-auto">
              Find the best local businesses, services, and experiences in your area.
              Connect with your community and support local entrepreneurs.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="flex flex-col sm:flex-row gap-3 p-2 bg-background rounded-lg border shadow-lg">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Search for businesses, services..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
              <div className="sm:w-48 relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  placeholder="Location"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-10 border-0 focus-visible:ring-0 focus-visible:ring-offset-0"
                />
              </div>
              <Button 
                onClick={handleSearch}
                className="sm:w-auto w-full"
                size="lg"
              >
                Search
              </Button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-2xl mx-auto pt-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {statsLoading ? (
                  <div className="h-8 w-16 bg-primary/20 animate-pulse rounded mx-auto"></div>
                ) : (
                  `${stats?.total_businesses || 500}+`
                )}
              </div>
              <div className="label text-muted-foreground">Local Businesses</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {statsLoading ? (
                  <div className="h-8 w-12 bg-primary/20 animate-pulse rounded mx-auto"></div>
                ) : (
                  `${stats?.total_categories || 50}+`
                )}
              </div>
              <div className="label text-muted-foreground">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {statsLoading ? (
                  <div className="h-8 w-16 bg-primary/20 animate-pulse rounded mx-auto"></div>
                ) : (
                  `${stats?.total_reviews || 1000}+`
                )}
              </div>
              <div className="label text-muted-foreground">Customer Reviews</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
