@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display:ital@0;1&family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --radius: 0.5rem;

  /* Design System Colors - Light Theme */
  --background: #f5f5ff;
  --foreground: #2a2a4a;
  --card: #ffffff;
  --card-foreground: #2a2a4a;
  --popover: #ffffff;
  --popover-foreground: #2a2a4a;
  --primary: #6e56cf;
  --primary-foreground: #ffffff;
  --secondary: #e4dfff;
  --secondary-foreground: #4a4080;
  --muted: #f0f0fa;
  --muted-foreground: #6c6c8a;
  --accent: #d8e6ff;
  --accent-foreground: #2a2a4a;
  --destructive: #ff5470;
  --destructive-foreground: #ffffff;
  --border: #e0e0f0;
  --input: #e0e0f0;
  --ring: #6e56cf;

  /* Charts */
  --chart-1: #6e56cf;
  --chart-2: #9e8cfc;
  --chart-3: #5d5fef;
  --chart-4: #7c75fa;
  --chart-5: #4740b3;

  /* Sidebar */
  --sidebar: #f0f0fa;
  --sidebar-foreground: #2a2a4a;
  --sidebar-primary: #6e56cf;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #d8e6ff;
  --sidebar-accent-foreground: #2a2a4a;
  --sidebar-border: #e0e0f0;
  --sidebar-ring: #6e56cf;

  /* Typography System */
  --font-family-serif: 'DM Serif Display', serif;
  --font-family-mono: 'Roboto Mono', monospace;
  --font-family-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-md: 18px;
  --font-size-lg: 20px;
  --font-size-xl: 24px;
  --font-size-2xl: 32px;
  --font-size-3xl: 40px;
  --font-size-4xl: 48px;

  --line-height-xs: 16px;
  --line-height-sm: 20px;
  --line-height-base: 24px;
  --line-height-md: 28px;
  --line-height-lg: 32px;
  --line-height-xl: 40px;
  --line-height-2xl: 48px;
  --line-height-3xl: 56px;
  --line-height-4xl: 64px;

  /* Spacing System (8pt Grid) */
  --space-0: 0px;
  --space-1: 8px;
  --space-2: 16px;
  --space-3: 24px;
  --space-4: 32px;
  --space-5: 40px;
  --space-6: 48px;
  --space-7: 56px;
  --space-8: 64px;
  --space-9: 72px;
  --space-10: 80px;

  /* Shadow System */
  --shadow-2xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-xs: 0px 4px 10px 0px hsl(240 30% 25% / 0.06);
  --shadow-sm: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 1px 2px -1px hsl(240 30% 25% / 0.12);
  --shadow-md: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 2px 4px -1px hsl(240 30% 25% / 0.12);
  --shadow-lg: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 4px 6px -1px hsl(240 30% 25% / 0.12);
  --shadow-xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.12), 0px 8px 10px -1px hsl(240 30% 25% / 0.12);
  --shadow-2xl: 0px 4px 10px 0px hsl(240 30% 25% / 0.30);
}

.dark {
  /* Design System Colors - Dark Theme */
  --background: #0f0f1a;
  --foreground: #e2e2f5;
  --card: #1a1a2e;
  --card-foreground: #e2e2f5;
  --popover: #1a1a2e;
  --popover-foreground: #e2e2f5;
  --primary: #a48fff;
  --primary-foreground: #0f0f1a;
  --secondary: #2d2b55;
  --secondary-foreground: #c4c2ff;
  --muted: #222244;
  --muted-foreground: #a0a0c0;
  --accent: #303060;
  --accent-foreground: #e2e2f5;
  --destructive: #ff5470;
  --destructive-foreground: #ffffff;
  --border: #303052;
  --input: #303052;
  --ring: #a48fff;

  /* Charts */
  --chart-1: #a48fff;
  --chart-2: #7986cb;
  --chart-3: #64b5f6;
  --chart-4: #4db6ac;
  --chart-5: #ff79c6;

  /* Sidebar */
  --sidebar: #1a1a2e;
  --sidebar-foreground: #e2e2f5;
  --sidebar-primary: #a48fff;
  --sidebar-primary-foreground: #0f0f1a;
  --sidebar-accent: #303060;
  --sidebar-accent-foreground: #e2e2f5;
  --sidebar-border: #303052;
  --sidebar-ring: #a48fff;
}

/* Smooth theme transitions - only on specific elements that need it */
html {
  transition: background-color 0.2s ease;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
  transition: background-color 0.2s ease, color 0.2s ease;
}

button {
  cursor: pointer;
}

@theme inline {
  /* Border Radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Typography */
  --font-family-serif: var(--font-family-serif);
  --font-family-mono: var(--font-family-mono);
  --font-family-sans: var(--font-family-sans);

  /* Font Sizes */
  --font-size-xs: var(--font-size-xs);
  --font-size-sm: var(--font-size-sm);
  --font-size-base: var(--font-size-base);
  --font-size-md: var(--font-size-md);
  --font-size-lg: var(--font-size-lg);
  --font-size-xl: var(--font-size-xl);
  --font-size-2xl: var(--font-size-2xl);
  --font-size-3xl: var(--font-size-3xl);
  --font-size-4xl: var(--font-size-4xl);

  /* Line Heights */
  --line-height-xs: var(--line-height-xs);
  --line-height-sm: var(--line-height-sm);
  --line-height-base: var(--line-height-base);
  --line-height-md: var(--line-height-md);
  --line-height-lg: var(--line-height-lg);
  --line-height-xl: var(--line-height-xl);
  --line-height-2xl: var(--line-height-2xl);
  --line-height-3xl: var(--line-height-3xl);
  --line-height-4xl: var(--line-height-4xl);

  /* Spacing */
  --spacing-0: var(--space-0);
  --spacing-1: var(--space-1);
  --spacing-2: var(--space-2);
  --spacing-3: var(--space-3);
  --spacing-4: var(--space-4);
  --spacing-5: var(--space-5);
  --spacing-6: var(--space-6);
  --spacing-7: var(--space-7);
  --spacing-8: var(--space-8);
  --spacing-9: var(--space-9);
  --spacing-10: var(--space-10);

  /* Shadows */
  --box-shadow-2xs: var(--shadow-2xs);
  --box-shadow-xs: var(--shadow-xs);
  --box-shadow-sm: var(--shadow-sm);
  --box-shadow: var(--shadow);
  --box-shadow-md: var(--shadow-md);
  --box-shadow-lg: var(--shadow-lg);
  --box-shadow-xl: var(--shadow-xl);
  --box-shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Typography Scale Classes */
  .text-xs {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
  }

  .text-sm {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
  }

  .text-base {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
  }

  .text-md {
    font-size: var(--font-size-md);
    line-height: var(--line-height-md);
  }

  .text-lg {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-lg);
  }

  .text-xl {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-xl);
  }

  .text-2xl {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-2xl);
  }

  .text-3xl {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-3xl);
  }

  .text-4xl {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-4xl);
  }

  /* Font Family Classes */
  .font-serif {
    font-family: var(--font-family-serif);
  }

  .font-mono {
    font-family: var(--font-family-mono);
  }

  .font-sans {
    font-family: var(--font-family-sans);
  }

  /* Semantic Heading Classes */
  h1, .h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-4xl);
    font-family: var(--font-family-serif);
    font-weight: 400;
  }

  h2, .h2 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-3xl);
    font-family: var(--font-family-serif);
    font-weight: 400;
  }

  h3, .h3 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-2xl);
    font-weight: 600;
  }

  h4, .h4 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-xl);
    font-weight: 600;
  }

  h5, .h5 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-lg);
    font-weight: 600;
  }

  /* Body Text Classes */
  .body-text {
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
  }

  .caption {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-xs);
    color: hsl(var(--muted-foreground));
  }

  .label {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
    font-weight: 500;
  }
}

/* Marquee animation for homepage banner */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 30s linear infinite;
}

/* Utility classes for text truncation */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Improve touch targets */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Optimize text sizes for mobile */
  h1 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  h2 {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  /* Improve form inputs on mobile */
  input, textarea, select {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  /* Better card spacing on mobile */
  .grid {
    gap: 1rem;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover\:shadow-lg:hover {
    box-shadow: var(--tw-shadow);
  }

  .hover\:-translate-y-1:hover {
    transform: translateY(0);
  }

  /* Add active states for touch feedback */
  button:active, [role="button"]:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}
