import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { categories, businesses, businessHours, reviews } from './schema/business';

// Database connection
const sql = postgres(process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/postgres');

const db = drizzle(sql);

// Helper function to create a URL-friendly slug
function createSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// Sample categories based on Belize business types
const sampleCategories = [
  {
    name: 'Hotels & Accommodations',
    slug: 'hotels-accommodations',
    icon: 'hotel',
    description: 'Hotels, resorts, inns, and other lodging facilities'
  },
  {
    name: 'Restaurants & Dining',
    slug: 'restaurants-dining',
    icon: 'utensils',
    description: 'Restaurants, cafes, bars, and dining establishments'
  },
  {
    name: 'Shopping & Retail',
    slug: 'shopping-retail',
    icon: 'store',
    description: 'Supermarkets, department stores, specialty shops, and retail outlets'
  },
  {
    name: 'Tours & Activities',
    slug: 'tours-activities',
    icon: 'compass',
    description: 'Tour operators, activity providers, and adventure services'
  },
  {
    name: 'Transportation',
    slug: 'transportation',
    icon: 'car',
    description: 'Airlines, taxi services, car rentals, and transportation providers'
  },
  {
    name: 'Healthcare & Medical',
    slug: 'healthcare-medical',
    icon: 'heart',
    description: 'Hospitals, clinics, pharmacies, and medical services'
  },
  {
    name: 'Financial Services',
    slug: 'financial-services',
    icon: 'briefcase',
    description: 'Banks, insurance companies, and financial institutions'
  },
  {
    name: 'Real Estate',
    slug: 'real-estate',
    icon: 'home',
    description: 'Real estate agencies, property development, and housing services'
  },
];

// Real businesses from Belize based on research
const belizeBusinesses = [
  // Hotels & Accommodations
  {
    name: 'Best Western Plus Belize Biltmore Plaza',
    categorySlug: 'hotels-accommodations',
    description: 'Premier hotel located on Northern Highway offering luxury accommodations with modern amenities, spa services, and fine dining. Features include the Bistro Restaurant, Jaguar Bar, Cabana Club fitness center, and Biltmore Spa.',
    shortDescription: 'Premier hotel with luxury accommodations, spa, and fine dining on Northern Highway',
    address: 'Mile 3, Northern Highway, Belize City, Belize',
    latitude: '17.5162',
    longitude: '-88.1808',
    phone: '+************',
    email: '<EMAIL>',
    website: 'https://belizebiltmore.com',
    isFeatured: true,
    averageRating: '4.2',
    totalReviews: 128
  },
  {
    name: 'Fort George Hotel & Spa',
    categorySlug: 'hotels-accommodations',
    description: 'Sophisticated hotel located in the heart of Belize City with a focus on sustainability and elegant accommodations. Located in the historic Fort George area.',
    shortDescription: 'Sophisticated hotel in historic Fort George area with spa services',
    address: 'Fort George, Belize City, Belize',
    latitude: '17.4956',
    longitude: '-88.1870',
    phone: '+************',
    website: 'https://www.fortgeorgebelize.com',
    isFeatured: true,
    averageRating: '4.1',
    totalReviews: 89
  },
  {
    name: 'Ramada by Wyndham Princess Belize City',
    categorySlug: 'hotels-accommodations',
    description: 'Full-service hotel featuring Seaview Restaurant, marina eatery, outdoor swimming pool, arcade with indoor bowling, gift shop, casino, cinema, nightclub, and spa services.',
    shortDescription: 'Full-service hotel with casino, cinema, spa, and marina dining',
    address: 'Newton Barracks, Belize City, Belize',
    latitude: '17.4923',
    longitude: '-88.1777',
    phone: '+************',
    website: 'https://www.wyndham.com',
    isFeatured: false,
    averageRating: '3.9',
    totalReviews: 156
  },
  {
    name: 'The Great House Inn',
    categorySlug: 'hotels-accommodations',
    description: '4-star hotel located in the historic Fort George Area, offering comfortable accommodations just 0.6 km from Belize City center and 14 km from the international airport.',
    shortDescription: '4-star historic hotel in Fort George area near city center',
    address: 'Fort George Area, Belize City, Belize',
    latitude: '17.4961',
    longitude: '-88.1875',
    phone: '+************',
    averageRating: '4.0',
    totalReviews: 67
  },

  // Restaurants & Dining
  {
    name: '501 Restaurant',
    categorySlug: 'restaurants-dining',
    description: 'Located on the ground floor of The Great House hotel, named after Belize\'s international telephone code. Doubles as a training facility for hospitality workers and serves innovative dishes including vegan options.',
    shortDescription: 'Innovative restaurant at The Great House hotel with vegan options',
    address: 'The Great House, Fort George, Belize City, Belize',
    latitude: '17.4961',
    longitude: '-88.1875',
    phone: '+************',
    isFeatured: true,
    averageRating: '4.5',
    totalReviews: 94
  },
  {
    name: 'The Victorian Room',
    categorySlug: 'restaurants-dining',
    description: 'Located at Best Western Plus Belize Biltmore Plaza, offering international cuisine for breakfast, lunch, and dinner in an elegant Victorian-themed setting.',
    shortDescription: 'Elegant Victorian-themed restaurant serving international cuisine',
    address: 'Best Western Plus Belize Biltmore Plaza, Mile 3, Northern Highway, Belize City',
    latitude: '17.5162',
    longitude: '-88.1808',
    phone: '+************',
    averageRating: '4.1',
    totalReviews: 76
  },
  {
    name: 'Vino Tintos',
    categorySlug: 'restaurants-dining',
    description: 'Highly rated restaurant in Belize City known for excellent service and quality cuisine. Popular among locals and tourists alike.',
    shortDescription: 'Highly rated restaurant known for excellent service and quality cuisine',
    address: 'Belize City, Belize',
    latitude: '17.4955',
    longitude: '-88.1865',
    isFeatured: true,
    averageRating: '4.7',
    totalReviews: 142
  },
  {
    name: 'Cheers Restaurant',
    categorySlug: 'restaurants-dining',
    description: 'Open-air restaurant near the Belize Zoo that attracts local farmers and zoo visitors. Known for its relaxed atmosphere and local cuisine.',
    shortDescription: 'Open-air restaurant near Belize Zoo with local cuisine',
    address: 'Near Belize Zoo, Western Highway, Belize',
    latitude: '17.3700',
    longitude: '-88.5500',
    phone: '+************',
    averageRating: '4.0',
    totalReviews: 53
  },

  // Shopping & Retail
  {
    name: 'Save-U Supermarket',
    categorySlug: 'shopping-retail',
    description: 'Modern supermarket located in Sancas Plaza offering groceries, liquor, sundries, and household items. Popular shopping destination for locals.',
    shortDescription: 'Modern supermarket with groceries, liquor, and household items',
    address: 'Sancas Plaza, Belize City, Belize',
    latitude: '17.4980',
    longitude: '-88.1820',
    phone: '+************',
    isFeatured: true,
    averageRating: '4.2',
    totalReviews: 87
  },
  {
    name: 'Brodies Department Store',
    categorySlug: 'shopping-retail',
    description: 'Modern supermarket, mini-department store and pharmacy on the Goldson Highway with free, safe parking. James Brodie & Co. has been serving Belize since 1887.',
    shortDescription: 'Historic department store and supermarket with pharmacy services',
    address: 'Goldson Highway, Belize City, Belize',
    latitude: '17.5100',
    longitude: '-88.1900',
    phone: '+************',
    website: 'https://www.brodies.bz',
    isFeatured: true,
    averageRating: '4.3',
    totalReviews: 121
  },
  {
    name: 'Commercial Center',
    categorySlug: 'shopping-retail',
    description: 'Two-story modern concrete structure with stalls and storefronts. First floor features fresh produce, fish stalls, butcher shops, flowers, and herbs. Second floor has souvenir shops and restaurants.',
    shortDescription: 'Two-story market with fresh produce, shops, and restaurants',
    address: 'South Side, Belize City, Belize',
    latitude: '17.4940',
    longitude: '-88.1890',
    averageRating: '3.8',
    totalReviews: 64
  },
  {
    name: 'Mirab Department Store',
    categorySlug: 'shopping-retail',
    description: 'Belize\'s largest department store institution, described as a sparkling, huge department store offering a wide variety of goods and services.',
    shortDescription: 'Belize\'s largest and most comprehensive department store',
    address: 'Belize City, Belize',
    latitude: '17.4960',
    longitude: '-88.1870',
    phone: '+************',
    isFeatured: false,
    averageRating: '4.0',
    totalReviews: 95
  },

  // Financial Services
  {
    name: 'Belize Bank Limited (BBL)',
    categorySlug: 'financial-services',
    description: 'The largest full-service commercial banking operation in Belize, providing comprehensive banking services to individuals and businesses throughout the country.',
    shortDescription: 'Largest full-service commercial bank in Belize',
    address: '60 Market Square, Belize City, Belize',
    latitude: '17.4945',
    longitude: '-88.1875',
    phone: '+************',
    website: 'https://www.belizebank.com',
    isFeatured: true,
    averageRating: '3.9',
    totalReviews: 203
  },

  // Tours & Activities
  {
    name: 'Tourism Village',
    categorySlug: 'tours-activities',
    description: 'Shopping complex with around 50 gift shops, including MOHO chocolate shop, duty-free stores, jewelry stores, clean restrooms, cybercafé, tour kiosks, and restaurants.',
    shortDescription: 'Shopping complex with 50+ gift shops, tours, and restaurants',
    address: 'Fort Street Tourism Village, Belize City, Belize',
    latitude: '17.4950',
    longitude: '-88.1885',
    phone: '+************',
    isFeatured: true,
    averageRating: '4.1',
    totalReviews: 156
  },

  // Healthcare & Medical
  {
    name: 'Belize Commercial Laundry and Linens',
    categorySlug: 'healthcare-medical',
    description: 'Premier laundry service provider for hotels, restaurants, and hospitals throughout Belize. Specialized commercial cleaning services with modern equipment.',
    shortDescription: 'Premier commercial laundry service for hotels and hospitals',
    address: 'Belize City, Belize',
    latitude: '17.4970',
    longitude: '-88.1850',
    phone: '+************',
    averageRating: '4.4',
    totalReviews: 32
  }
];

async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    console.log('✅ Connected to database');

    // Insert categories
    console.log('📝 Inserting categories...');
    const insertedCategories = await db.insert(categories)
      .values(sampleCategories)
      .returning();
    
    console.log(`✅ Inserted ${insertedCategories.length} categories`);

    // Create category lookup map
    const categoryMap = new Map();
    insertedCategories.forEach(cat => {
      categoryMap.set(cat.slug, cat.id);
    });

    // Insert businesses
    console.log('🏢 Inserting businesses...');
    for (const business of belizeBusinesses) {
      const categoryId = categoryMap.get(business.categorySlug);
      if (!categoryId) {
        console.warn(`⚠️ Category not found for business: ${business.name}`);
        continue;
      }

      const insertedBusiness = await db.insert(businesses)
        .values({
          name: business.name,
          slug: createSlug(business.name),
          category_id: categoryId,
          description: business.description,
          short_description: business.shortDescription,
          address: business.address,
          latitude: business.latitude,
          longitude: business.longitude,
          phone: business.phone,
          email: business.email || null,
          website: business.website || null,
          is_featured: business.isFeatured || false,
          average_rating: business.averageRating || '0.00',
          total_reviews: business.totalReviews || 0,
        })
        .returning();

      console.log(`✅ Inserted business: ${business.name}`);

      // Add standard business hours (Mon-Fri 8AM-6PM, Sat 9AM-5PM, Closed Sunday)
      const standardHours = [
        { day: 'monday', open: '08:00', close: '18:00', closed: false },
        { day: 'tuesday', open: '08:00', close: '18:00', closed: false },
        { day: 'wednesday', open: '08:00', close: '18:00', closed: false },
        { day: 'thursday', open: '08:00', close: '18:00', closed: false },
        { day: 'friday', open: '08:00', close: '18:00', closed: false },
        { day: 'saturday', open: '09:00', close: '17:00', closed: false },
        { day: 'sunday', open: null, close: null, closed: true },
      ];

      await db.insert(businessHours)
        .values(standardHours.map(hour => ({
          business_id: insertedBusiness[0].id,
          day_of_week: hour.day as any,
          open_time: hour.open,
          close_time: hour.close,
          is_closed: hour.closed,
        })));

      // Add some sample reviews
      if (business.totalReviews && business.totalReviews > 0) {
        const sampleReviews = [
          {
            rating: 5,
            comment: 'Excellent service and great location! Highly recommended.',
            author_name: 'Maria Rodriguez',
            is_verified: true,
            is_approved: true,
          },
          {
            rating: 4,
            comment: 'Good experience overall. Staff was friendly and helpful.',
            author_name: 'John Smith',
            is_verified: true,
            is_approved: true,
          },
          {
            rating: 5,
            comment: 'Outstanding quality and value. Will definitely return!',
            author_name: 'Sarah Johnson',
            is_verified: true,
            is_approved: true,
          },
        ];

        // Insert 1-3 random reviews
        const numReviews = Math.min(3, Math.floor(Math.random() * 3) + 1);
        for (let i = 0; i < numReviews; i++) {
          await db.insert(reviews)
            .values({
              business_id: insertedBusiness[0].id,
              ...sampleReviews[i],
            });
        }
      }
    }

    console.log('🎉 Database seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - ${insertedCategories.length} categories inserted`);
    console.log(`   - ${belizeBusinesses.length} businesses inserted`);
    console.log(`   - Business hours and sample reviews added`);

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await sql.end();
    console.log('🔌 Database connection closed');
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase().catch(console.error);
}

export { seedDatabase };